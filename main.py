import asyncio
import json
import os
import signal
import threading
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from typing import Optional

import numpy as np
import requests
import sounddevice as sd
import soundfile as sf

from agent_client import knowledge_agent_client

# Configuration Constants
SAMPLE_RATE = 16000
CHANNELS = 1
DTYPE = np.int16
CHUNK_MS = 40  # ~25 fps, recommended small chunks for streaming
MAX_THREADS = 4

# Global Variables
executor: Optional[ThreadPoolExecutor] = None
shutdown_event = threading.Event()
tts_lock = threading.Lock()
is_playing = threading.Event()

# Async primitives
audio_queue: Optional[asyncio.Queue] = None
ws_send_task: Optional[asyncio.Task] = None
ws_recv_task: Optional[asyncio.Task] = None
mic_stream: Optional[sd.InputStream] = None

# Deepgram config
DEEPGRAM_API_KEY = os.getenv("DEEPGRAM_API_KEY")
DEEPGRAM_LISTEN_URL = (
    "wss://api.deepgram.com/v1/listen?model=nova-2-general&smart_format=true"
    "&punctuate=true&vad_events=true&encoding=linear16&sample_rate=16000"
)
DEEPGRAM_SPEAK_URL = "https://api.deepgram.com/v1/speak"
DEEPGRAM_VOICE_MODEL = "aura-asteria-en"  # default Deepgram voice model

def setup_signal_handler():
    signal.signal(signal.SIGINT, lambda s, f: shutdown_event.set())

def ensure_deepgram_config():
    if not DEEPGRAM_API_KEY or not DEEPGRAM_API_KEY.strip():
        raise RuntimeError(
            "DEEPGRAM_API_KEY is not set. Please set it in your environment before running."
        )
    print("🔐 Deepgram API key loaded from environment.")

async def process_audio_stream():
    """Start microphone stream and two coroutines to send/receive with Deepgram."""
    global audio_queue, ws_send_task, ws_recv_task, mic_stream

    import websockets  # lazy import to keep startup fast

    async def ws_sender(ws):
        while not shutdown_event.is_set():
            chunk = await audio_queue.get()
            if chunk is None:
                break
            try:
                await ws.send(chunk)
            except Exception:
                break

    async def ws_receiver(ws):
        while not shutdown_event.is_set():
            try:
                msg = await ws.recv()
            except Exception:
                break
            try:
                data = json.loads(msg)
            except Exception:
                continue

            # Handle Deepgram events
            if data.get("type") == "Results":
                ch = data.get("channel", {})
                alts = ch.get("alternatives", [])
                if not alts:
                    continue
                transcript = alts[0].get("transcript", "").strip()
                is_final = data.get("is_final", False)
                if is_final and transcript:
                    print(f"📝 Transcribed: {transcript}")
                    # Handle response on a worker to avoid blocking receiver
                    asyncio.get_running_loop().run_in_executor(
                        executor, create_and_play_response, transcript
                    )
            elif data.get("type") == "UtteranceEnd":
                # utterance boundary detected by Deepgram VAD
                pass

    def sd_callback(indata, frames, time_info, status):
        if shutdown_event.is_set() or is_playing.is_set():
            return
        # Send raw PCM bytes, but handle queue full gracefully
        try:
            audio_queue.put_nowait(indata.copy().tobytes())
        except asyncio.QueueFull:
            # Drop audio data if queue is full to prevent blocking
            pass

    print("🎧 Listening (Deepgram live)... Press Ctrl+C to exit")
    audio_queue = asyncio.Queue(maxsize=200)

    # Open microphone stream
    mic_stream = sd.InputStream(
        channels=CHANNELS,
        samplerate=SAMPLE_RATE,
        dtype=DTYPE,
        blocksize=int(SAMPLE_RATE * CHUNK_MS / 1000),
        callback=sd_callback,
    )
    mic_stream.start()

    # Connect to Deepgram listen websocket
    headers = {"Authorization": f"Token {DEEPGRAM_API_KEY}"}
    async with websockets.connect(
        DEEPGRAM_LISTEN_URL,
        additional_headers=headers,
        ping_interval=20,
        ping_timeout=20,
        max_size=10 * 1024 * 1024,
    ) as ws:
        ws_send_task = asyncio.create_task(ws_sender(ws))
        ws_recv_task = asyncio.create_task(ws_receiver(ws))
        await asyncio.wait([ws_send_task, ws_recv_task], return_when=asyncio.FIRST_COMPLETED)

    # Cleanup
    if mic_stream:
        mic_stream.stop()
        mic_stream.close()
    if ws_send_task and not ws_send_task.done():
        ws_send_task.cancel()
    if ws_recv_task and not ws_recv_task.done():
        ws_recv_task.cancel()

def create_and_play_response(prompt: str):
    """Call LLM for response, synthesize with Deepgram Speak, and play audio.
    Runs in a thread pool to avoid blocking the event loop.
    """
    with tts_lock:
        if shutdown_event.is_set():
            return

        is_playing.set()  # Pause mic streaming to avoid feedback
        try:
            agent_response = knowledge_agent_client(prompt) or "I'm sorry, I couldn't process that."
            print(f"🤖 Response: {agent_response}")

            # Request WAV audio from Deepgram Speak
            headers = {
                "Authorization": f"Token {DEEPGRAM_API_KEY}",
                "Accept": "audio/wav",
                "Content-Type": "application/json",
            }
            payload = {
                "text": agent_response,
                "model": DEEPGRAM_VOICE_MODEL,
            }
            resp = requests.post(DEEPGRAM_SPEAK_URL, headers=headers, json=payload, timeout=60)
            resp.raise_for_status()

            import io

            with io.BytesIO(resp.content) as bio:
                data, sr = sf.read(bio, dtype="float32")
                if data.ndim == 1:
                    data = data.reshape(-1, 1)
                sd.play(data, sr)
                sd.wait()
        except Exception as e:
            print(f"❌ Error in TTS: {str(e)}")
        finally:
            is_playing.clear()  # Resume mic streaming

def main():
    global executor
    setup_signal_handler()
    ensure_deepgram_config()
    executor = ThreadPoolExecutor(max_workers=MAX_THREADS)
    try:
        asyncio.run(process_audio_stream())
    finally:
        shutdown_event.set()
        if executor:
            executor.shutdown(wait=True)
        print("👋 Exiting...")

if __name__ == "__main__":
    main()